import { Injectable } from '@nestjs/common';
import { HobbiesSearchOriginEnum } from 'src/constants/enums/hobbies-search-origin-enum';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import { AddHobbiesByHobbyIdParams, SearchHobbiesParams } from './models/hobbies.params';
import { SearchHobbiesReturn } from './models/hobbies.returns';

@Injectable()
export class HobbiesService {
  constructor(private dataSource: DataSource) {}

  async searchHobbies(searchHobbiesParams: SearchHobbiesParams): Promise<SearchHobbiesReturn> {
    const { type, sessionId, q, limit, page } = searchHobbiesParams;

    const qClause = q ? `AND h.hobby_name ILIKE '${q}%' OR ph.hobby_name ILIKE '${q}%'` : 'TRUE';

    const result =
      sessionId && type === HobbiesSearchOriginEnum.PROFILE
        ? await this.dataSource.query(`
      WITH UserHobbyRecords AS (
          -- Get all hobbies that the user has
          SELECT 
              COUNT(hobby_id) AS is_added,
              hobby_id
          FROM user_hobbies
          WHERE user_id = '${sessionId}'
          GROUP BY hobby_id
      )
      SELECT
          h.id AS hobby_id,
          h.hobby_name AS hobby_name,
          CASE
            WHEN ph.hobby_name IS NOT NULL THEN ph.hobby_name
            ELSE 'Uncategorized'
          END AS category,
          uhr.is_added AS is_added
      FROM hobbies h
      LEFT JOIN hobbies ph ON ph.id = h.parent_hobby_id
      LEFT JOIN UserHobbyRecords uhr ON uhr.hobby_id = h.id
      WHERE ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `)
        : await this.dataSource.query(`
      SELECT
          h.id AS hobby_id,
          h.hobby_name AS hobby_name,
          CASE
            WHEN ph.hobby_name IS NOT NULL THEN ph.hobby_name
            ELSE 'Uncategorized'
          END AS category
      FROM hobbies h
      LEFT JOIN hobbies ph ON ph.id = h.parent_hobby_id
      LEFT JOIN UserHobbyRecords uhr ON uhr.hobby_id = h.id
      WHERE ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    const categories = <string[]>[...new Set(result.map((val) => val.category))];
    const hobbies: Record<
      string,
      {
        hobbyId: string;
        hobbyName: string;
        isAdded: boolean;
      }[]
    > = {};

    for (const c of categories) {
      const categoryHobbies = result.filter((val) => val.category === c);
      if (!Object.keys(hobbies).includes(c)) {
        hobbies[c] = [];
      }
      hobbies[c]?.push(
        ...categoryHobbies.map((val) => ({
          hobbyId: val.hobby_id,
          hobbyName: val.hobby_name,
          isAdded: val.is_added ? true : false,
        })),
      );
    }

    return {
      // hobbies,
      hobbies: [],
      hobbyCount: result.length,
    };
  }

  async addHobbiesByHobbyId(addHobbiesByHobbyIdParams: AddHobbiesByHobbyIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, hobbyIds } = addHobbiesByHobbyIdParams;

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_hobbies',
        values: hobbyIds.map((val) => ({
          user_id: sessionId,
          hobby_id: val,
        })),
      }),
    );

    return {};
  }
}
