import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString, IsUUID, IsUrl } from 'class-validator';

export class CreatePageDto {
  @ApiProperty({
    type: 'string',
    description: 'Name of the page',
    example: 'Photography Club Istanbul',
    required: true,
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  pageName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the page thumbnail image',
    example: 'https://example.com/page-thumbnail.jpg',
    required: false,
    format: 'url',
  })
  @IsUrl({}, { message: 'Thumbnail URL must be a valid URL' })
  @IsOptional()
  thumbnailUrl!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Website URL for the page',
    example: 'https://photographyclub.com',
    required: false,
    format: 'url',
  })
  @IsUrl({}, { message: 'Website URL must be a valid URL' })
  @IsOptional()
  websiteUrl!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Description of the page',
    example: 'A community for photography enthusiasts in Istanbul',
    required: false,
  })
  @IsString()
  @IsOptional()
  description!: string | null;

  @ApiProperty({
    type: 'boolean',
    description: 'Whether this page is educational',
    example: false,
    required: true,
  })
  @IsBoolean()
  isEdu!: boolean;

  @ApiProperty({
    type: 'boolean',
    description: 'Whether this page has membership functionality',
    example: true,
    required: true,
  })
  @IsBoolean()
  haveMembership!: boolean;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'uuid' },
    description: 'Array of hobby tag UUIDs associated with the page',
    example: ['123e4567-e89b-12d3-a456-************'],
    required: true,
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each hobby tag ID must be a valid UUID v4' })
  tagIds!: string[];

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'uuid' },
    description: 'Array of creator user UUIDs for the page',
    example: ['123e4567-e89b-12d3-a456-************'],
    required: true,
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each creator ID must be a valid UUID v4' })
  creatorIds!: string[];

  @ApiProperty({
    type: 'string',
    description: 'UUID of the location where the page is based',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Location ID must be a valid UUID v4' })
  locationId!: string;
}

export class RemoveFollowerByPageIdDto {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the user to remove as a follower',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'User ID must be a valid UUID v4' })
  userId!: string;
}

export class UpdateDescriptionByPageIdDto {
  @ApiProperty({
    type: 'string',
    description: 'New description for the page',
    example: 'Updated description with new information about our photography community',
    required: true,
  })
  @IsString()
  newDescription!: string;
}

export class UpdateLinksByPageIdDto {
  @ApiProperty({
    type: 'string',
    description: 'New website link for the page',
    example: 'https://newwebsite.com',
    required: true,
    format: 'url',
  })
  @IsUrl({}, { message: 'New link must be a valid URL' })
  newLink!: string;
}

export class UpdateLocationByPageIdDto {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the new location for the page',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'New location ID must be a valid UUID v4' })
  newLocationId!: string;
}
