import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserRoleEnum } from 'src/entities';
import {
  IventListItem,
  IventListItemWithIsFavorited,
  SideMenuPageItem,
  UserListItem,
  UserListItemWithPhoneNumber,
  UserListItemWithRelationshipStatus,
} from 'src/models';
import { MemoryFolderCardItem } from 'src/models/memory-folder-card-item';
import { VibeFolderCardItem } from 'src/models/vibe-folder-card-item';

export class GetContactsByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItemWithPhoneNumber),
    },
    description: 'List of user contacts with their phone numbers',
    example: [],
  })
  contacts!: UserListItemWithPhoneNumber[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of contacts found',
    example: 0,
    minimum: 0,
  })
  contactCount!: number;
}

export class GetFavoritesByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(IventListItemWithIsFavorited),
    },
    description: "List of user's favorite ivents",
    example: [],
  })
  ivents!: IventListItemWithIsFavorited[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of favorite ivents',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class GetFollowerFriendsByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of friends who are also followers',
    example: [],
  })
  friends!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of follower friends',
    example: 0,
    minimum: 0,
  })
  friendCount!: number;
}

export class GetFollowersByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    description: 'List of friend usernames',
    example: [],
  })
  friendUsernames!: string[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of friends',
    example: 2,
    minimum: 0,
  })
  friendCount!: number;

  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItemWithRelationshipStatus),
    },
    description: 'List of followers with their relationship status',
    example: [],
  })
  followers!: UserListItemWithRelationshipStatus[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of followers',
    example: 0,
    minimum: 0,
  })
  followerCount!: number;

  @ApiProperty({
    type: 'boolean',
    description: "Whether this is the current user's own profile",
    example: false,
  })
  isFirstPerson!: boolean;
}

export class GetFollowingsByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItemWithRelationshipStatus),
    },
    description: 'List of users being followed',
    example: [],
  })
  followings!: UserListItemWithRelationshipStatus[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of users being followed',
    example: 0,
    minimum: 0,
  })
  followingCount!: number;
}

export class GetIventsByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(IventListItem),
    },
    description: 'List of ivents created by the user',
    example: [],
  })
  ivents!: IventListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of ivents created by the user',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;

  @ApiProperty({
    type: 'boolean',
    description: "Whether this is the current user's own profile",
    example: false,
  })
  isFirstPerson!: boolean;
}

export class GetLevelByUserIdReturn {
  @ApiProperty({
    type: 'string',
    enum: Object.values(UserRoleEnum),
    description: 'User level (role) information',
    example: UserRoleEnum.LEVEL_0,
  })
  levelInfo!: UserRoleEnum;
}

export class GetPagesByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(SideMenuPageItem),
    },
    description: 'List of pages the user is associated with',
    example: [],
  })
  pages!: SideMenuPageItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of pages',
    example: 0,
    minimum: 0,
  })
  pageCount!: number;
}

export class GetMemoryFoldersByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(MemoryFolderCardItem),
    },
    description: "List of user's memory folders",
    example: [],
  })
  memoryFolders!: MemoryFolderCardItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of memory folders',
    example: 0,
    minimum: 0,
  })
  memoryFolderCount!: number;
}

export class GetUserByUserIdReturn {
  @ApiProperty({
    type: 'string',
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId!: string;

  @ApiProperty({
    type: 'string',
    enum: Object.values(UserRoleEnum),
    description: 'Role of the user',
    example: UserRoleEnum.LEVEL_0,
  })
  userRole!: UserRoleEnum;

  @ApiProperty({
    type: 'string',
    description: 'Username of the user',
    example: 'john_doe',
  })
  username!: string;

  @ApiProperty({
    type: 'string',
    description: 'Full name of the user',
    example: 'John Doe',
  })
  fullname!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl!: string | null;

  @ApiProperty({
    type: 'number',
    description: 'Number of ivents created by the user',
    example: 5,
    minimum: 0,
  })
  iventCount!: number;

  @ApiProperty({
    type: 'number',
    description: 'Number of friends the user has',
    example: 10,
    minimum: 0,
  })
  friendCount!: number;

  @ApiProperty({
    type: 'number',
    description: 'Number of followers the user has',
    example: 15,
    minimum: 0,
  })
  followerCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    description: "List of user's hobbies",
    example: ['Photography', 'Travel'],
  })
  hobbies!: string[];

  @ApiProperty({
    type: 'boolean',
    description: 'Whether the current user is following this user',
    example: false,
  })
  isFollowing!: boolean;

  @ApiProperty({
    type: 'boolean',
    description: 'Whether the current user is friends with this user',
    example: false,
  })
  isFriend!: boolean;

  @ApiProperty({
    type: 'boolean',
    description: "Whether this is the current user's own profile",
    example: false,
  })
  isFirstPerson!: boolean;
}

export class GetVibeFoldersByUserIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(VibeFolderCardItem),
    },
    description: "List of user's vibe folders",
    example: [],
  })
  vibeFolders!: VibeFolderCardItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of vibe folders',
    example: 0,
    minimum: 0,
  })
  vibeFolderCount!: number;
}

export class RegisterReturn {
  @ApiProperty({
    type: 'string',
    description: 'Unique identifier of the newly registered user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId!: string;

  @ApiProperty({
    type: 'string',
    description: 'JWT authentication token for the new user',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token!: string;

  @ApiProperty({
    type: 'string',
    enum: Object.values(UserRoleEnum),
    description: 'Role of the new user',
    example: UserRoleEnum.LEVEL_0,
  })
  role!: UserRoleEnum;

  @ApiProperty({
    type: 'string',
    description: 'Username of the new user',
    example: 'john_doe',
  })
  username!: string;

  @ApiProperty({
    type: 'string',
    description: 'Full name of the new user',
    example: 'John Doe',
  })
  fullname!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl!: string | null;
}

export class GetUserBannerByUserIdReturn {
  @ApiProperty({
    type: 'string',
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Username of the user',
    example: 'john_doe',
  })
  username!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl!: string | null;

  @ApiProperty({
    type: 'string',
    description: 'Full name of the user',
    example: 'John Doe',
  })
  fullname!: string;
}
