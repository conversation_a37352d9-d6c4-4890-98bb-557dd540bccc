import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { HobbyItem } from 'src/models';

export class SearchHobbiesReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(HobbyItem) },
    description: 'List of hobby categories',
    example: [],
  })
  hobbies!: HobbyItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of hobby categories',
    example: 2,
    minimum: 0,
  })
  hobbyCount!: number;
}
