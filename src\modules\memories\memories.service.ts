import { Injectable } from '@nestjs/common';
import { randomBytes } from 'crypto';
import { MemoryOriginEnum } from 'src/constants/enums/memory-origin-enum';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import { CreateMemoryParams, DeleteMemoryByMemoryIdParams, GetMemoryByMemoryIdParams } from './models/memories.params';
import { CreateMemoryReturn, GetMemoryByMemoryIdReturn } from './models/memories.returns';

@Injectable()
export class MemoriesService {
  constructor(private dataSource: DataSource) {
    // this.s3 = new S3Client({
    //   region: process.env.AWS_REGION,
    //   credentials: {
    //     accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    //     secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    //   },
    // });
  }

  async createMemory(createMemoryParams: CreateMemoryParams): Promise<CreateMemoryReturn> {
    // TODO: ADD BUFFER OPTION! AWS S3
    const { sessionId, sessionRole, mediaFormat, caption, squadId } = createMemoryParams;

    const memoryFolderId = this.dataSource
      .query(`SELECT memory_folder_id FROM squads WHERE id = ${squadId};`)
      .then((result) => result[0].memory_folder_id);

    const randomFileHex = (bytes = 32) => randomBytes(bytes).toString('hex');
    // const fileExtension = file.originalname.split('.').pop();
    // const fileKey = `${memoryFolderId}/${randomFileHex()}.${fileExtension}`;

    const insertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'memories',
        values: {
          media_url: 'fileKey',
          media_format: mediaFormat,
          caption,
          memory_folder_id: memoryFolderId,
          creator_page_id: sessionRole === 'page' ? sessionId : null,
          creator_user_id: sessionRole === 'user' ? sessionId : null,
        },
      }),
    );
    const insertedMemoryId = insertResult[0].id;

    // const command = new PutObjectCommand({
    //   Bucket: process.env.AWS_MEDIA_BUCKET_NAME,
    //   Key: fileKey,
    //   Body: file.buffer,
    //   ContentType: file.mimetype,
    // });
    // await this.s3.send(command);

    return { memoryId: insertedMemoryId };
  }

  async deleteMemoryByMemoryId(deleteMemoryByMemoryIdParams: DeleteMemoryByMemoryIdParams): Promise<EmptyReturn> {
    const { ...rest } = deleteMemoryByMemoryIdParams;
    return {};
  }

  async getMemoryByMemoryId(getMemoryByMemoryIdParams: GetMemoryByMemoryIdParams): Promise<GetMemoryByMemoryIdReturn> {
    const { sessionId, sessionRole, memoryId, origin } = getMemoryByMemoryIdParams;

    const result =
      origin === MemoryOriginEnum.CREATE_NEW
        ? await this.dataSource.query(`
      SELECT
          m.id AS memory_id,
          m.media_url AS media_url,
          m.thumbnail_url AS thumbnail_url,
          m.creator_user_id AS creator_id,
          u.avatar_url AS creator_avatar_url,
          u.username AS creator_username,
          m.caption AS caption,
          i.id AS ivent_id,
          i.name AS ivent_name,

        ( 
          SELECT
            CONCAT (
              ARRAY_TO_STRING((ARRAY_AGG(u.firstname))[:2], ','),
              '|',
              COUNT(other_member_id)
            )
          FROM squad_friendships
          LEFT JOIN squads sub_s ON sub_s.id = squad_id
          LEFT JOIN users sub_u ON sub_u.id = other_member_id
          WHERE member_id = '${sessionId}'
          AND sub_s.ivent_id = i.id
          GROUP BY sub_s.id
        ) AS member_summary,

      FROM memories m
      LEFT JOIN users u ON u.id = m.creator_user_id
      LEFT JOIN squads s ON s.memory_folder_id = m.id
      LEFT JOIN ivents i ON i.id = s.ivent_id
    `)
        : await this.dataSource.query(`
      SELECT
        m.id AS memory_id,
        m.media_url AS media_url,
        m.thumbnail_url AS thumbnail_url,
        m.creator_user_id AS creator_id,
        u.avatar_url AS creator_avatar_url,
        u.username AS creator_username,
        m.caption AS caption,
        i.id AS ivent_id,
        i.name AS ivent_name

      FROM memories m
        LEFT JOIN users u ON u.id = m.creator_user_id
        LEFT JOIN squads s ON s.memory_folder_id = m.id
        LEFT JOIN ivents i ON i.id = s.ivent_id
        LEFT JOIN ivent_dates d ON d.ivent_id = i.id;
    `);

    const defaultReturn = {
      memoryId: result[0].memory_id,
      mediaUrl: '',
      thumbnailUrl: result[0].thumbnail_url,
      creatorId: result[0].creator_id,
      creatorAvatarUrl: result[0].creator_avatar_url,
      creatorUsername: result[0].creator_username,
      caption: result[0].caption,
      iventId: result[0].ivent_id,
      iventName: result[0].ivent_name,
    };

    return {
      ...defaultReturn,
      memberFirstnames: result[0].member_summary ? result[0].member_summary.split('|')[0].split(',') : [],
      memberCount: result[0].member_summary ? Number(result[0].member_summary.split('|')[1]) : 0,
      dates: result[0].date,
    };
  }
}
