import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID } from 'class-validator';

export class AddHobbiesByHobbyIdDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'uuid' },
    description: "Array of hobby UUIDs to add to the user's profile",
    example: ['123e4567-e89b-12d3-a456-************'],
    required: true,
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each hobby ID must be a valid UUID v4' })
  hobbyIds!: string[];
}
