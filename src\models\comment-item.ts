import { ApiProperty } from '@nestjs/swagger';

export class CommentItem {
  @ApiProperty({
    type: 'string',
    format: 'uuid',
    description: 'UUID of the comment',
  })
  commentId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Text content of the comment',
  })
  comment!: string;

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    description: 'UUID of the user who made the comment',
  })
  commenterUserId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Username of the user who made the comment',
    example: 'john_doe',
  })
  commenterUsername!: string;

  @ApiProperty({
    type: 'string',
    format: 'date-time',
    description: 'Timestamp when the comment was created',
  })
  createdAt!: string;
}
