import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum } from 'src/entities';

export class BasicAccountListItem {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the account (user or page)',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  accountId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Name of the account (user or page)',
    example: '<PERSON> or Photography Club Istanbul',
  })
  accountName!: string;

  @ApiProperty({
    type: 'string',
    enum: Object.values(AccountTypeEnum),
    description: 'Type of the account (user or page)',
    example: AccountTypeEnum.USER,
  })
  accountType!: AccountTypeEnum;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the account image',
    example: 'https://example.com/account-image.jpg',
    format: 'url',
  })
  accountImageUrl!: string | null;
}
