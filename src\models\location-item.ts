import { ApiProperty } from '@nestjs/swagger';

export class LocationItem {
  @ApiProperty({
    type: 'string',
    format: 'uuid',
    description: 'UUID of the location',
    example: '123e4567-e89b-12d3-a456-************',
  })
  locationId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Mapbox place identifier',
    example: 'address.1234567890',
  })
  mapboxId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Name of the location',
    example: 'Central Park',
  })
  locationName!: string;

  @ApiProperty({
    type: 'string',
    description: 'Detailed address of the location',
    example: '5th Avenue and 59th Street, Manhattan, New York',
  })
  openAddress!: string;

  @ApiProperty({
    type: 'number',
    description: 'Latitude coordinate of the location',
    example: 40.7766,
    minimum: -90,
    maximum: 90,
  })
  latitude!: number;

  @ApiProperty({
    type: 'number',
    description: 'Longitude coordinate of the location',
    example: -73.9712,
    minimum: -180,
    maximum: 180,
  })
  longitude!: number;

  @ApiProperty({
    type: 'string',
    description: 'State where the location is situated',
    example: 'New York',
  })
  state!: string;
}
