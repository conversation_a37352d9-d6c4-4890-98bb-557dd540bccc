import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  IsUrl,
  Matches,
  ValidateNested,
} from 'class-validator';
import { AccountTypeEnum, IventPrivacyEnum } from 'src/entities';

export class CollabDto {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the collaborator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
    required: true,
  })
  @IsUUID('4', { message: 'Collaborator ID must be a valid UUID v4' })
  id!: string;

  @ApiProperty({
    type: 'string',
    enum: Object.values(AccountTypeEnum),
    description: 'Type of the collaborator account',
    example: AccountTypeEnum.USER,
    required: true,
  })
  @IsEnum(AccountTypeEnum, { message: 'Type must be a valid AccountTypeEnum value' })
  type!: AccountTypeEnum;
}

export class CreateIventDto {
  @ApiProperty({
    type: 'string',
    enum: Object.values(AccountTypeEnum),
    description: 'Type of the creator account',
    example: AccountTypeEnum.USER,
    required: true,
  })
  @IsEnum(AccountTypeEnum, { message: 'Creator type must be a valid AccountTypeEnum value' })
  creatorType!: AccountTypeEnum;

  @ApiProperty({
    type: 'string',
    description: 'Ivent name can only contain letters, numbers, underscores, and hyphens',
    example: 'my_awesome_ivent',
    required: true,
    minLength: 3,
    maxLength: 200,
  })
  @IsString()
  @Matches(/^[a-zA-Z0-9_\-]{3,200}$/, {
    message: 'Ivent name must contain only letters, numbers, underscores, and hyphens (3-200 characters)',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the ivent thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    required: false,
    format: 'url',
  })
  @IsUrl({}, { message: 'Thumbnail URL must be a valid URL' })
  @IsOptional()
  thumbnailUrl!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Base64 encoded thumbnail image buffer',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...',
    required: false,
  })
  @IsString()
  @IsOptional()
  thumbnailBuffer!: string | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'date-time' },
    description: 'Array of date strings in date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
    required: true,
  })
  @IsArray()
  @IsString({ each: true, message: 'Each date must be a string' })
  dates!: string[];

  @ApiProperty({
    type: 'string',
    description: 'Mapbox place ID for location',
    example: 'address.1234567890',
    required: true,
  })
  @IsString()
  mapboxId!: string;

  @ApiProperty({
    type: 'number',
    description: 'Latitude coordinate of the ivent location',
    example: 41.0082,
    required: true,
    minimum: -90,
    maximum: 90,
  })
  @IsNumber({}, { message: 'Latitude must be a valid number' })
  latitude!: number;

  @ApiProperty({
    type: 'number',
    description: 'Longitude coordinate of the ivent location',
    example: 28.9784,
    required: true,
    minimum: -180,
    maximum: 180,
  })
  @IsNumber({}, { message: 'Longitude must be a valid number' })
  longitude!: number;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Detailed description of the ivent',
    example: 'Join us for an amazing outdoor photography workshop!',
    required: false,
  })
  @IsString()
  @IsOptional()
  description!: string | null;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the category tag for this ivent',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Category tag ID must be a valid UUID v4' })
  categoryTagId!: string;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'uuid' },
    description: 'Array of hobby tag UUIDs associated with this ivent',
    example: ['123e4567-e89b-12d3-a456-************'],
    required: true,
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each hobby tag must be a valid UUID v4' })
  tagIds!: string[];

  @ApiProperty({
    type: 'string',
    enum: Object.values(IventPrivacyEnum),
    description: 'Privacy setting for the ivent',
    example: IventPrivacyEnum.PUBLIC,
    required: true,
  })
  @IsEnum(IventPrivacyEnum, { message: 'Privacy must be a valid IventPrivacyEnum value' })
  privacy!: IventPrivacyEnum;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    description: 'Array of university codes that are allowed to join this ivent',
    example: ['BOGAZICI', 'ITU'],
    required: true,
  })
  @IsArray()
  @IsString({ each: true, message: 'Each university code must be a string' })
  allowedUniversityCodes!: string[];

  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(CollabDto) },
    description: 'Array of collaborators for this ivent',
    example: [],
    required: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CollabDto)
  collabs!: CollabDto[];

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to Google Forms for registration',
    example: 'https://forms.google.com/...',
    required: false,
    format: 'url',
  })
  @IsUrl({}, { message: 'Google Forms URL must be a valid URL' })
  @IsOptional()
  googleFormsUrl!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Instagram username for the ivent',
    example: 'my_ivent_account',
    required: false,
  })
  @IsString()
  @IsOptional()
  instagramUsername!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'WhatsApp group URL',
    example: 'https://chat.whatsapp.com/...',
    required: false,
    format: 'url',
  })
  @IsUrl({}, { message: 'WhatsApp URL must be a valid URL' })
  @IsOptional()
  whatsappUrl!: string | null;

  @ApiProperty({
    type: 'boolean',
    nullable: true,
    description: 'Whether the WhatsApp URL should be kept private',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isWhatsappUrlPrivate!: boolean | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'WhatsApp contact number',
    example: '+90(555)1234567',
    required: false,
  })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  @IsOptional()
  whatsappNumber!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Phone number for calls',
    example: '+90(555)1234567',
    required: false,
  })
  @Matches(/^\+\d{1,3}\(\d{3}\)\d{7}$/, {
    message: 'Phone number must be in format +XX(XXX)XXXXXXX',
  })
  @IsOptional()
  callNumber!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Website URL for the ivent',
    example: 'https://myivent.com',
    required: false,
    format: 'url',
  })
  @IsUrl({}, { message: 'Website URL must be a valid URL' })
  @IsOptional()
  websiteUrl!: string | null;
}

export class UpdateDateByIventIdDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'date-time' },
    description: 'Array of new date strings in date-time format',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
    required: true,
  })
  @IsArray()
  @IsString({ each: true, message: 'Each date must be a string' })
  newDates!: string[];
}

export class UpdateDetailsByIventIdDto {
  @ApiProperty({
    type: 'string',
    description: 'New description for the ivent',
    example: 'Updated description with new information',
    required: true,
  })
  @IsString()
  newDescription!: string;
}

export class UpdateLocationByIventIdDto {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the new location',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'Location ID must be a valid UUID v4' })
  newlocationId!: string;
}

export class GetBannerByIventIdDto {
  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'uuid' },
    description: 'Array of ivent UUIDs to get banner information for',
    example: ['123e4567-e89b-12d3-a456-************'],
    required: true,
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each ivent ID must be a valid UUID v4' })
  iventIds!: string[];
}
