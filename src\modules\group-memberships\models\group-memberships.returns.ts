import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserListItem } from 'src/models/user-list-item';

export class SearchGroupMembersByGroupIdReturn {
  @ApiProperty({
    allOf: [
      { $ref: getSchemaPath(UserListItem) },
      {
        type: 'object',
        properties: {
          role: {
            type: 'string',
            description: 'Role of the member in the group (admin, moderator, member)',
            example: 'member',
          },
          isFriend: {
            type: 'boolean',
            description: 'Whether this member is a friend of the current user',
            example: true,
          },
        },
      },
    ],
    description: 'List of group members with their roles and friendship status',
    example: [],
  })
  members!: (UserListItem & { role: string; isFriend: boolean })[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of group members',
    example: 25,
    minimum: 0,
  })
  memberCount!: number;
}

export class SearchInvitableUsersByGroupIdReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(UserListItem) },
    description: 'List of users that can be invited to the group',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of invitable users',
    example: 10,
    minimum: 0,
  })
  userCount!: number;
}

export class SearchUsersForGroupCreationReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(UserListItem) },
    description: 'List of users available for group creation',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of users available for group creation',
    example: 15,
    minimum: 0,
  })
  userCount!: number;
}
