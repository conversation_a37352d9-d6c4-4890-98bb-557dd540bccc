import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, map } from 'rxjs';
import { DataSource } from 'typeorm';
import { GetLatestLocationsParams, GetLocationsParams, GetPlacesParams } from './models/locations.params';
import { GetLatestLocationsReturn, GetLocationsReturn, GetPlacesReturn } from './models/locations.returns';
import { PlacesApiResponse } from './models/locations.types';

@Injectable()
export class LocationsService {
  constructor(
    private dataSource: DataSource,
    private configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async getLocations(getLocationsParams: GetLocationsParams): Promise<GetLocationsReturn> {
    const { ...rest } = getLocationsParams;
    return {
      locations: [],
      locationCount: 0,
    };
  }

  async getLatestLocations(getLatestLocationsParams: GetLatestLocationsParams): Promise<GetLatestLocationsReturn> {
    const { ...rest } = getLatestLocationsParams;
    return {
      locations: [],
      locationCount: 0,
    };
  }

  // deprecated
  // TODO: Mapbox ile değiştirilecek
  async getPlaces(getPlacesParams: GetPlacesParams): Promise<GetPlacesReturn> {
    const { sessionId, sessionRole, lat, lng, radius } = getPlacesParams;
    const apiKey = this.configService.get<string>('GOOGLE_MAPS_API_KEY');
    const baseUrl = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
    const response = await firstValueFrom(
      this.httpService
        .get<PlacesApiResponse>(baseUrl, {
          params: {
            key: apiKey,
            location: `${lat},${lng}`,
            radius,
          },
        })
        .pipe(map((response) => response.data)),
    );

    const locationsResult = await this.dataSource.query(`
      SELECT
          id AS location_id,
          location_name,
          open_address,
          ST_X(ST_AsText(geom::geometry)) AS longitude,
          ST_Y(ST_AsText(geom::geometry)) AS latitude
      FROM locations
      WHERE google_maps_id IS NULL
      AND ST_DWithin(
          geom::geography, 
          ST_SetSRID(ST_MakePoint(${lng}, ${lat}), 4326)::geography, 
          ${radius}
      );
    `);

    return {
      places: [
        ...locationsResult.map((val) => {
          return {
            latitude: val.latitude,
            longitude: val.longitude,
            locationName: val.location_name,
            openAddress: val.open_address,
            location_id: val.location_id,
            type: '',
          };
        }),
        ...response.results.map((val) => {
          return {
            latitude: val.geometry.location.lat,
            longitude: val.geometry.location.lng,
            locationName: val.name,
            openAddress: val.vicinity,
            mapboxId: val.place_id,
            type: '',
          };
        }),
      ],
      placeCount: response.results.length,
    };
  }
}
