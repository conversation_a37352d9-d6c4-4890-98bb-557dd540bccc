import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum, IventViewTypeEnum } from 'src/entities';

export class IventListItem {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the ivent thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl!: string | null;

  @ApiProperty({
    type: 'string',
    description: 'Name of the ivent location',
    example: 'Central Park',
  })
  locationName!: string;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'date-time' },
    description: 'List of dates for the ivent',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  dates!: string[];

  @ApiProperty({
    type: 'string',
    description: 'UUID of the ivent creator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creatorId!: string;

  @ApiProperty({
    type: 'string',
    enum: Object.values(AccountTypeEnum),
    description: 'Type of the ivent creator',
    example: AccountTypeEnum.USER,
  })
  creatorType!: AccountTypeEnum;

  @ApiProperty({
    type: 'string',
    description: 'Username of the ivent creator',
    example: 'john_doe',
  })
  creatorUsername!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the ivent creator image',
    example: 'https://example.com/creator-image.jpg',
    format: 'url',
  })
  creatorImageUrl!: string | null;

  @ApiProperty({
    type: 'number',
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[];

  @ApiProperty({
    type: 'array',
    items: { type: 'string', nullable: true, format: 'url' },
    description: 'List of member avatar URLs in the ivent',
    example: ['https://example.com/avatar1.jpg', 'https://example.com/avatar2.jpg'],
  })
  memberAvatarUrls!: (string | null)[];

  @ApiProperty({
    type: 'string',
    enum: Object.values(IventViewTypeEnum),
    description: 'View type of the ivent for the current user',
    example: IventViewTypeEnum.DEFAULT,
  })
  viewType!: IventViewTypeEnum;
}

export class IventListItemWithIsFavorited extends IventListItem {
  @ApiProperty({
    type: 'boolean',
    description: 'Whether the ivent is favorited by the current user',
    example: true,
  })
  isFavorited!: boolean;
}
