import { ApiProperty } from '@nestjs/swagger';

export class VibeFolderCardItem {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeFolderId!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the vibe folder thumbnail image',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl!: string | null;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the latest vibe in the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeId!: string;

  @ApiProperty({
    type: 'number',
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[];
}
