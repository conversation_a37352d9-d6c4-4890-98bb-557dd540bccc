import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { IventCardItem } from 'src/models/ivent-card-item';
import { UserListItem } from 'src/models/user-list-item';
import { VibeFolderCardItem } from 'src/models/vibe-folder-card-item';

export class CreatePageReturn {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the newly created page',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  pageId!: string;
}

export class GetIventsCreatedByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(IventCardItem),
    },
    description: 'List of ivents created by this page',
    example: [],
  })
  ivents!: IventCardItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of ivents created by this page',
    example: 0,
    minimum: 0,
  })
  iventCount!: number;
}

export class GetPageByPageIdReturn {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the page',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  pageId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Name of the page',
    example: 'Photography Club Istanbul',
  })
  pageName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the page thumbnail image',
    example: 'https://example.com/page-thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl!: string | null;

  @ApiProperty({
    type: 'number',
    description: 'Number of ivents created by this page',
    example: 5,
    minimum: 0,
  })
  createdIventCount!: number;

  @ApiProperty({
    type: 'number',
    description: 'Number of followers of this page',
    example: 150,
    minimum: 0,
  })
  followerCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'uuid' },
    description: 'Hobby tag IDs associated with the page',
    example: ['123e4567-e89b-12d3-a456-************'],
  })
  tagIds!: string[];

  @ApiProperty({
    type: 'boolean',
    description: 'Whether this page has membership functionality',
    example: true,
  })
  haveMembership!: boolean;

  @ApiProperty({
    type: 'boolean',
    description: 'Whether the current user is the owner/creator of this page',
    example: false,
  })
  isFirstPerson!: boolean;
}

export class GetPageDetailsByPageIdReturn {
  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Description of the page',
    example: 'A community for photography enthusiasts in Istanbul',
  })
  description!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Website URL for the page',
    example: 'https://photographyclub.com',
    format: 'url',
  })
  websiteUrl!: string | null;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the location where the page is based',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  locationId!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Address of the page location',
    example: 'Taksim Square, Istanbul, Turkey',
  })
  locationAdress!: string | null;
}

export class GetVibeFoldersByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(VibeFolderCardItem),
    },
    description: 'List of vibe folders associated with this page',
    example: [],
  })
  vibeFolders!: VibeFolderCardItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of vibe folders',
    example: 0,
    minimum: 0,
  })
  vibeFolderCount!: number;
}

export class SearchFollowersByPageIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItem),
    },
    description: 'List of users who follow this page',
    example: [],
  })
  users!: UserListItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of followers',
    example: 0,
    minimum: 0,
  })
  userCount!: number;
}
