import { ApiProperty } from '@nestjs/swagger';
import { AccountTypeEnum, MediaFormatEnum } from 'src/entities';

export class VibeItem {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the vibe',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeId!: string;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeFolderId!: string;

  @ApiProperty({
    type: 'string',
    description: 'URL to the vibe media',
    example: 'https://example.com/vibe.jpg',
    format: 'url',
  })
  mediaUrl!: string;

  @ApiProperty({
    type: 'enum',
    enum: Object.values(MediaFormatEnum),
    description: 'Format of the media',
    example: MediaFormatEnum.IMAGE,
  })
  mediaFormat!: MediaFormatEnum;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the vibe thumbnail',
    example: 'https://example.com/thumbnail.jpg',
    format: 'url',
  })
  thumbnailUrl!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Caption of the vibe',
    example: 'This is a vibe',
  })
  caption!: string;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the creator',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creatorId!: string;

  @ApiProperty({
    type: 'string',
    enum: Object.values(AccountTypeEnum),
    description: 'Type of the creator',
    example: AccountTypeEnum.USER,
  })
  creatorType!: AccountTypeEnum;

  @ApiProperty({
    type: 'string',
    description: 'Username of the creator',
    example: 'john_doe',
  })
  creatorUsername!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'URL to the creator image',
    example: 'https://example.com/creator-image.jpg',
    format: 'url',
  })
  creatorAvatarUrl!: string | null;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the ivent',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  iventId!: string;

  @ApiProperty({
    type: 'string',
    description: 'Name of the ivent',
    example: 'My Awesome Ivent',
  })
  iventName!: string;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'date-time' },
    description: 'List of dates for the ivent',
    example: ['2024-08-31T22:00:00Z', '2024-09-01T14:00:00Z'],
  })
  dates!: string[];

  @ApiProperty({
    type: 'number',
    description: 'Number of members in the ivent',
    example: 10,
    minimum: 0,
  })
  memberCount!: number;

  @ApiProperty({
    type: 'array',
    items: { type: 'string' },
    description: "List of member's first names in the ivent",
    example: ['John', 'Jane'],
  })
  memberFirstnames!: string[];

  @ApiProperty({
    type: 'number',
    description: 'Number of likes on the vibe',
    example: 10,
    minimum: 0,
  })
  likeCount!: number;

  @ApiProperty({
    type: 'number',
    description: 'Number of comments on the vibe',
    example: 5,
    minimum: 0,
  })
  commentCount!: number;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'UUID of the next vibe in the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  nextVibeId!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'UUID of the previous vibe in the vibe folder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  previousVibeId!: string | null;

  @ApiProperty({
    type: 'number',
    description: 'Index of the vibe in the vibe folder',
    example: 0,
    minimum: 0,
  })
  vibeIndex!: number;

  @ApiProperty({
    type: 'number',
    description: 'Total number of vibes in the vibe folder',
    example: 10,
    minimum: 0,
  })
  vibeCount!: number;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Date of creation of the vibe',
    example: '2024-08-31T22:00:00Z',
    format: 'date-time',
  })
  createdAt!: string | null;
}
