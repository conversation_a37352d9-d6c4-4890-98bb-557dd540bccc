import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, IsUUID, Matches } from 'class-validator';

export class CreateGroupDto {
  @ApiProperty({
    type: 'string',
    description: 'Group name can only contain letters, numbers, underscores, hyphens, and periods',
    example: 'My Awesome Group',
    required: true,
    minLength: 3,
    maxLength: 50,
  })
  @Matches(/^[a-zA-Z0-9_\-\.]{3,50}$/, {
    message: 'Group name can only contain letters, numbers, underscores, hyphens, and periods (3-50 characters)',
  })
  groupName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Base64 encoded thumbnail image buffer for the group',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...',
    required: false,
  })
  @IsString()
  @IsOptional()
  thumbnailBuffer!: string | null;

  @ApiProperty({
    type: 'array',
    items: { type: 'string', format: 'uuid' },
    description: 'Array of user UUIDs to add as group members',
    example: ['123e4567-e89b-12d3-a456-************'],
    required: true,
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each user ID must be a valid UUID v4' })
  userIds!: string[];
}
