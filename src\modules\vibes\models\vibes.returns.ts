import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { UserListItemWithRelationshipStatus } from 'src/models';
import { CommentItem } from 'src/models/comment-item';
import { VibeItem } from 'src/models/vibe-item';

export class CreateVibeReturn {
  @ApiProperty({
    type: 'string',
    description: 'UUID of the newly created vibe',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  vibeId!: string;
}

export class GetCommentsByVibeIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(CommentItem),
    },
    description: 'List of comments on the vibe',
    example: [],
  })
  comments!: CommentItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of comments on the vibe',
    example: 0,
    minimum: 0,
  })
  commentCount!: number;
}

export class GetLikesByVibeIdReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(UserListItemWithRelationshipStatus),
    },
    description: 'List of users who liked the vibe',
    example: [],
  })
  likes!: UserListItemWithRelationshipStatus[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of likes on the vibe',
    example: 0,
    minimum: 0,
  })
  likeCount!: number;
}

export class GetVibeByVibeIdReturn extends VibeItem {}

export class GetVibesReturn {
  @ApiProperty({
    type: 'array',
    items: {
      $ref: getSchemaPath(VibeItem),
    },
    description: 'List of vibes',
    example: [],
  })
  vibes!: VibeItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of vibes',
    example: 0,
    minimum: 0,
  })
  vibeCount!: number;
}
