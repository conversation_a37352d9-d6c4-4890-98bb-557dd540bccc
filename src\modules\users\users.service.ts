import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { sign } from 'jsonwebtoken';
import { UserRoleEnum } from 'src/entities';
import { EmptyReturn } from 'src/models/empty-return';
import { insertQueryBuilder } from 'src/utils/insert-query-builder';
import { DataSource } from 'typeorm';
import {
  DeleteUserByUserIdParams,
  FollowByUserIdParams,
  GetContactsByUserIdParams,
  GetFavoritesByUserIdParams,
  GetFollowerFriendsByUserIdParams,
  GetFollowersByUserIdParams,
  GetFollowingsByUserIdParams,
  GetIventsByUserIdParams,
  GetLevelByUserIdParams,
  GetMemoryFoldersByUserIdParams,
  GetPagesByUserIdParams,
  GetUserBannerByUserIdParams,
  GetUserByUserIdParams,
  GetVibeFoldersByUserIdParams,
  RegisterParams,
  RemoveFollowerByUserIdParams,
  SavePhoneContactsParams,
  SendCreatorRequestFormParams,
  SendVerificationEmailParams,
  SubscribeByUserIdParams,
  UnfollowByUserIdParams,
  UnsubscribeByUserIdParams,
  UpdateByUserIdParams,
  UpdateEmailByUserIdParams,
  UpdateGradByUserIdParams,
  UpdateNotificationsByUserIdParams,
  UpdatePhoneNumberByUserIdParams,
  UpdatePrivacyByUserIdParams,
  ValidateEmailParams,
} from './models/users.params';
import {
  GetContactsByUserIdReturn,
  GetFavoritesByUserIdReturn,
  GetFollowerFriendsByUserIdReturn,
  GetFollowersByUserIdReturn,
  GetFollowingsByUserIdReturn,
  GetIventsByUserIdReturn,
  GetLevelByUserIdReturn,
  GetMemoryFoldersByUserIdReturn,
  GetPagesByUserIdReturn,
  GetUserBannerByUserIdReturn,
  GetUserByUserIdReturn,
  GetVibeFoldersByUserIdReturn,
  RegisterReturn,
} from './models/users.returns';

@Injectable()
export class UsersService {
  constructor(private dataSource: DataSource) {}

  async register(params: RegisterParams): Promise<RegisterReturn> {
    // Initialize
    const { phoneNumber, fullname, hobbyIds } = params;

    // Split name
    // TODO: Check the length of fullname
    const nameArray = fullname.replace(/\s+/g, ' ').split(' ');
    const lastname = nameArray.pop();
    const firstname = nameArray.join(' ');

    // Find unique username
    let username = `${firstname}.${lastname}`;
    const existingUsernames = await this.dataSource.query(`
        SELECT username
        FROM users
        WHERE username LIKE '${username}%';
    `);
    while (true) {
      username = `${firstname}.${lastname}${Math.floor(Math.random() * 10000)}`;
      const condition1 = !existingUsernames.some((val) => val.username === username);
      const condition2 = username.length > 3 && username.length < 30;
      if (condition1 && condition2) break;
    }

    // Insert user
    const userInsertResult = await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'users',
        values: {
          phone_number: phoneNumber,
          firstname,
          lastname,
          username,
        },
      }),
    );
    const insertedUserId = userInsertResult[0].id;

    // Insert user hobbies
    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_hobbies',
        values: hobbyIds.map((val) => ({
          user_id: insertedUserId,
          hobby_id: val,
        })),
      }),
    );

    // Extra

    // Save and return token, role and userId
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET environment variable is not defined');
    }
    const token = sign({ _id: insertedUserId, role: 'level_0' }, process.env.JWT_SECRET as string, {
      expiresIn: '7d',
    });
    return {
      userId: insertedUserId,
      token,
      role: UserRoleEnum.LEVEL_0,
      username,
      fullname: `${firstname} ${lastname}`,
      avatarUrl: 'https://i.pravatar.cc/300',
    };
  }

  async deleteByUserId(deleteByUserIdParams: DeleteUserByUserIdParams): Promise<EmptyReturn> {
    // TODO: LOOK
    const { sessionId, sessionRole, userId } = deleteByUserIdParams;
    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);
    await this.dataSource.createQueryBuilder().delete().from('users').where({ id: userId }).execute();
    return {};
  }

  async getByUserId(params: GetUserByUserIdParams): Promise<GetUserByUserIdReturn> {
    const { sessionId, sessionRole, userId } = params;

    const isFirstPerson = sessionId === userId;

    const query = `
      WITH UserIventStats AS (
          SELECT COUNT(ivent_id) AS ivent_count, account_id
          FROM ivent_users
          WHERE status IN ('accepted', 'joined', 'admin')
          GROUP BY account_id
      ),
      UserFriendStats AS (
          SELECT COUNT(friend_id) AS friend_count, user_id
          FROM user_friendships
          GROUP BY user_id
      ),
      UserFollowerStats AS (
          SELECT COUNT(follower_id) AS follower_count, following_id
          FROM user_followers
          GROUP BY following_id
      ),
      UserHobbyList AS (
          SELECT STRING_AGG(DISTINCT h.hobby_name, ',') AS hobbies, uh.user_id
          FROM user_hobbies uh
          LEFT JOIN hobbies h ON h.id = uh.hobby_id
          GROUP BY uh.user_id
      )
      SELECT 
          u.id AS user_id, u.role AS user_role, u.username, 
          CONCAT(u.firstname, ' ', u.lastname) AS fullname, u.avatar_url,
          COALESCE(uis.ivent_count, 0) AS ivent_count,
          COALESCE(ufrs.friend_count, 0) AS friend_count,
          COALESCE(ufos.follower_count, 0) AS follower_count,
          COALESCE(uhl.hobbies, '') AS hobbies,
          EXISTS (
              SELECT 1 
              FROM user_followers 
              WHERE follower_id = $1 AND following_id = u.id
          ) AS is_following,
          EXISTS (
              SELECT 1 
              FROM user_friendships 
              WHERE user_id = $1 AND friend_id = u.id
          ) AS is_friend
      FROM users u
      LEFT JOIN UserIventStats uis ON uis.account_id = u.id
      LEFT JOIN UserFriendStats ufrs ON ufrs.user_id = u.id
      LEFT JOIN UserFollowerStats ufos ON ufos.following_id = u.id
      LEFT JOIN UserHobbyList uhl ON uhl.user_id = u.id
      WHERE u.id = $2;`;

    const [userProfile] = await this.dataSource.query(query, [sessionId, userId]);
    if (!userProfile) throw new HttpException('NOT_FOUND', HttpStatus.NOT_FOUND);

    return {
      userId: userProfile.user_id,
      userRole: userProfile.user_role,
      username: userProfile.username,
      fullname: userProfile.fullname,
      avatarUrl: userProfile.avatar_url,
      iventCount: parseInt(userProfile.ivent_count),
      friendCount: parseInt(userProfile.friend_count),
      followerCount: parseInt(userProfile.follower_count),
      hobbies: userProfile.hobbies ? userProfile.hobbies.split(',') : [],
      isFollowing: !!userProfile.is_following,
      isFriend: !!userProfile.is_friend,
      isFirstPerson,
    };
  }

  async getContactsByUserId(params: GetContactsByUserIdParams): Promise<GetContactsByUserIdReturn> {
    const { phoneNumbers, sessionId, sessionRole, userId, limit, page } = params;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const query = `
      SELECT u.id AS user_id, u.username, u.phone_number,
          u.avatar_url, u.university_code AS university
      FROM users u
      WHERE u.phone_number IN (${phoneNumbers.map((val) => `'${val}'`).join(',')});
    `;

    const queryResult = await this.dataSource.query(query);

    return {
      contacts: queryResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        phoneNumber: val.phone_number,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      contactCount: queryResult.length,
    };
  }

  async getFavoritesByUserId(params: GetFavoritesByUserIdParams): Promise<GetFavoritesByUserIdReturn> {
    const { sessionId, sessionRole, userId, q, limit, page } = params;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const qClause = q ? `i.ivent_name ILIKE '${q}%'` : 'TRUE';

    const query = `
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          d.date AS date,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          asi.view_type AS view_type,
          CASE
              WHEN asi.view_type = 'joined' THEN msoss.member_count
              WHEN asi.view_type = 'created' THEN csof.collab_count
          END AS member_count,
          CASE
              WHEN asi.view_type = 'joined' THEN msoss.member_first_names
              WHEN asi.view_type = 'created' THEN csof.collab_names
          END AS member_names,
          CASE
              WHEN asi.view_type = 'joined' THEN msoss.member_avatar_urls
              WHEN asi.view_type = 'created' THEN csof.collab_thumbnail_urls
          END AS member_avatar_urls
      FROM user_favorites uf
      LEFT JOIN ivents i ON i.id = uf.favorited_ivent_id
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      LEFT JOIN ivent_dates_aggregated d ON d.ivent_id = i.id
      LEFT JOIN active_session_ivents asi ON asi.ivent_id = i.id
      LEFT JOIN member_summary_of_session_squad msoss ON msoss.ivent_id = i.id
      LEFT JOIN collab_summary_of_ivent csof ON csof.ivent_id = i.id
      WHERE uf.user_id = $1
      AND asi.account_id = $1
      AND msoss.user_id = $1
      AND csof.account_id = $1
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `;

    const queryResult = await this.dataSource.query(query, [sessionId]);

    return {
      ivents: queryResult.map((row) => ({
        iventId: row.ivent_id,
        iventName: row.ivent_name,
        thumbnailUrl: row.thumbnail_url,
        locationName: row.location_name,
        date: row.date,
        creatorId: row.creator_id,
        creatorType: row.creator_type,
        creatorName: row.creator_name,
        creatorImageUrl: row.creator_image_url,
        memberAvatarUrls: row.member_avatar_urls ? row.member_avatar_urls.split(',') : [],
        memberCount: row.member_count ? Number(row.member_count) : 0,
        memberNames: row.member_names ? row.member_names.split(',') : [],
        viewType: row.view_type ?? 'default',
      })),
      iventCount: queryResult.length,
    };
  }

  async getFollowingsByUserId(params: GetFollowingsByUserIdParams): Promise<GetFollowingsByUserIdReturn> {
    const { sessionId, sessionRole, userId, q, limit, page } = params;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const query = `
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_followers uf
      LEFT JOIN users u ON uf.following_id = u.id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      WHERE uf.follower_id = '${userId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `;

    const queryResult = await this.dataSource.query(query);

    return {
      followings: queryResult.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      followingCount: queryResult.length,
    };
  }

  async getIventsByUserId(params: GetIventsByUserIdParams): Promise<GetIventsByUserIdReturn> {
    const { sessionId, sessionRole, userId, type, q, limit, page } = params;

    const isFirstPerson = sessionId === userId;

    const qClause = q ? `i.ivent_name ILIKE '${q}%'` : 'TRUE';
    const typeClause = type === 'joined' || sessionRole !== 'creator' ? 'TRUE' : 'FALSE';
    const iventType = type === 'joined' || sessionRole !== 'creator' ? 'member' : 'user';

    const query = `
      SELECT
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          i.thumbnail_url AS thumbnail_url,
          d.date AS date,
          l.location_name AS location_name,
          i.creator_type AS creator_type,
          COALESCE(p.id, u.id, dis.id) AS creator_id,
          COALESCE(p.page_name, u.username, dis.distributor_name) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url, dis.thumbnail_url) AS creator_image_url,
          asi.view_type AS view_type,
          CASE
              WHEN ${typeClause} THEN msoss.member_count
              ELSE csof.collab_count
          END AS member_count,
          CASE
              WHEN ${typeClause} THEN msoss.member_first_names
              ELSE csof.collab_names
          END AS member_names,
          CASE
              WHEN ${typeClause} THEN msoss.member_avatar_urls
              ELSE csof.collab_thumbnail_urls
          END AS member_avatar_urls
      FROM ivent_users iu
      LEFT JOIN ivents i ON i.id = iu.ivent_id
      LEFT JOIN locations l ON l.id = i.location_id
      LEFT JOIN pages p ON p.id = i.creator_page_id
      LEFT JOIN users u ON u.id = i.creator_user_id
      LEFT JOIN distributors dis ON dis.id = i.creator_distributor_id
      LEFT JOIN ivent_dates_aggregated d ON d.ivent_id = i.id
      LEFT JOIN active_session_ivents asi ON asi.ivent_id = i.id
      LEFT JOIN member_summary_of_session_squad msoss ON msoss.ivent_id = i.id
      LEFT JOIN collab_summary_of_ivent csof ON csof.ivent_id = i.id
      WHERE iu.account_id = '${userId}'
      AND asi.account_id = '${userId}'
      AND msoss.user_id = '${userId}'
      AND csof.account_id = '${userId}'
      AND ${qClause}
      AND iu.type = '${iventType}'
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `;

    const queryResult = await this.dataSource.query(query);

    return {
      ivents: queryResult.map((val) => ({
        iventId: val.ivent_id,
        iventName: val.ivent_name,
        thumbnailUrl: val.thumbnail_url,
        locationName: val.location_name,
        date: val.date,
        creatorId: val.creator_id,
        creatorType: val.creator_type,
        creatorName: val.creator_name,
        creatorImageUrl: val.creator_image_url,
        memberAvatarUrls: val.member_avatar_urls ? val.member_avatar_urls.split(',') : [],
        memberCount: val.member_count ? Number(val.member_count) : 0,
        memberNames: val.member_names ? val.member_names.split(',') : [],
        viewType: val.view_type,
      })),
      iventCount: queryResult.length,
      isFirstPerson,
    };
  }

  async getLevelByUserId(getLevelByUserIdParams: GetLevelByUserIdParams): Promise<GetLevelByUserIdReturn> {
    const { sessionId, sessionRole, userId } = getLevelByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const levelResult = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.role AS level_info
      FROM users u
      WHERE u.id = '${userId}';
    `);

    return {
      levelInfo: levelResult[0].level_info,
    };
  }

  async getPagesByUserId(getPagesByUserIdParams: GetPagesByUserIdParams): Promise<GetPagesByUserIdReturn> {
    const { sessionId, sessionRole, userId } = getPagesByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const pagesResult = await this.dataSource.query(`
      SELECT
          p.id AS page_id,
          pm.status AS page_role,
          p.page_name AS page_name,
          p.thumbnail_url AS thumbnail_url
      FROM page_memberships pm
      LEFT JOIN pages p ON p.id = pm.page_id
      WHERE pm.member_id = '${userId}'
      AND pm.status IN ('admin', 'moderator');
    `);

    return {
      pages: pagesResult.map((val) => ({
        pageId: val.page_id,
        pageRole: val.page_role,
        pageName: val.page_name,
        thumbnailUrl: val.thumbnail_url,
      })),
      pageCount: pagesResult.length,
    };
  }

  async getMemoryFoldersByUserId(
    getMemoryFoldersByUserIdParams: GetMemoryFoldersByUserIdParams,
  ): Promise<GetMemoryFoldersByUserIdReturn> {
    const { sessionId, sessionRole, userId, limit, page } = getMemoryFoldersByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    return {
      memoryFolders: [
        {
          memoryFolderId: '1',
          thumbnailUrl: 'https://picsum.photos/200',
          iventId: '1',
          iventName: 'Ivent Name 1',
          dates: ['2021-01-01'],
          memberFirstnames: ['Member 1', 'Member 2'],
          memberCount: 2,
          createdAt: null,
        },
        {
          memoryFolderId: '2',
          thumbnailUrl: 'https://picsum.photos/200',
          iventId: '2',
          iventName: 'Ivent Name 2',
          dates: ['2021-01-01'],
          memberFirstnames: ['Member 1', 'Member 2'],
          memberCount: 2,
          createdAt: null,
        },
      ],
      memoryFolderCount: 2,
    };
  }

  async getVibeFoldersByUserId(params: GetVibeFoldersByUserIdParams): Promise<GetVibeFoldersByUserIdReturn> {
    const { sessionId, sessionRole, userId, limit, page } = params;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const query = `
      WITH
          RecentVibes AS (
              SELECT DISTINCT
                  ON (vf.id) vf.id AS vibe_folder_id,
                  v.id AS vibe_id,
                  v.created_at AS created_at
              FROM vibe_folders vf
                  LEFT JOIN vibes v ON v.vibe_folder_id = vf.id
              ORDER BY vf.id, v.created_at ASC
          )
      SELECT
          vf.id AS vibe_folder_id,
          vf.thumbnail_url AS thumbnail_url,
          i.id AS ivent_id,
          i.ivent_name AS ivent_name,
          msoss.member_count AS member_count,
          msoss.member_first_names AS member_first_names,
          msoss.member_avatar_urls AS member_avatar_urls,
          rv.vibe_id AS vibe_id
      FROM
          squad_memberships AS sm
          LEFT JOIN squads s ON s.id = sm.squad_id
          LEFT JOIN ivents i ON i.id = s.ivent_id
          LEFT JOIN vibe_folders vf ON vf.id = s.vibe_folder_id
          LEFT JOIN member_summary_of_session_squad msoss ON msoss.ivent_id = i.id
          LEFT JOIN RecentVibes rv ON rv.vibe_folder_id = vf.id
      WHERE sm.member_id = $1
      AND sm.status IN ('accepted', 'joined')
      AND msoss.user_id = $1
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `;

    const queryResult = await this.dataSource.query(query, [userId]);
    return {
      vibeFolders: queryResult
        .filter((row) => row.vibe_id != null)
        .map((row) => ({
          vibeFolderId: row.vibe_folder_id,
          thumbnailUrl: row.thumbnail_url,
          iventId: row.ivent_id,
          iventName: row.ivent_name,
          memberNames: row.member_summary ? row.member_summary.split('|')[0].split(',') : [],
          memberCount: row.member_summary ? Number(row.member_summary.split('|')[1]) : 0,
          vibeId: row.vibe_id,
        })),
      vibeFolderCount: queryResult.length,
    };
  }

  async updateByUserId(updateByUserIdParams: UpdateByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId, newUsername, newBirthday, newGender, newAvatarUrl } = updateByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .update('users')
      .set({
        username: newUsername,
        birthday: newBirthday,
        gender: newGender,
        avatar_url: newAvatarUrl,
      })
      .where({ id: userId })
      .execute();

    return {};
  }

  async updateEmailByUserId(updateEmailByUserIdParams: UpdateEmailByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId, newEmail } = updateEmailByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .update('users')
      .set({
        email: newEmail,
      })
      .where({ id: userId })
      .execute();

    return {};
  }

  async updateGradByUserId(updateGradByUserIdParams: UpdateGradByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId, newGrad } = updateGradByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .update('users')
      .set({
        edu_verification: newGrad,
      })
      .where({ id: userId })
      .execute();

    return {};
  }

  async updateNotificationsByUserId(
    updateNotificationsByUserIdParams: UpdateNotificationsByUserIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = updateNotificationsByUserIdParams; // TODO ikiye ayıracağız
    return {};
  }

  async updatePhoneNumberByUserId(
    updatePhoneNumberByUserIdParams: UpdatePhoneNumberByUserIdParams,
  ): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId, newPhoneNumber } = updatePhoneNumberByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .update('users')
      .set({
        phone_number: newPhoneNumber,
      })
      .where({ id: userId })
      .execute();

    return {};
  }

  async updatePrivacyByUserId(updatePrivacyByUserIdParams: UpdatePrivacyByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = updatePrivacyByUserIdParams; // TODO ikiye ayıracağız
    return {};
  }

  async getFollowersByUserId(
    getFollowersByUserIdParams: GetFollowersByUserIdParams,
  ): Promise<GetFollowersByUserIdReturn> {
    const { sessionId, sessionRole, userId, q, limit, page } = getFollowersByUserIdParams;

    const isFirstPerson = sessionId === userId;
    const isCreator = sessionRole === 'creator';
    if (!isCreator) throw new HttpException('BAD_REQUEST', HttpStatus.BAD_REQUEST);

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const friendsResult = q
      ? null
      : await this.dataSource.query(`
      SELECT
          ARRAY_TO_STRING((ARRAY_AGG(u.firstname))[:3], ',') AS friend_firstnames,
          COUNT(u.id) AS friend_count
      FROM user_followers ufo
      INNER JOIN user_friendships ufr ON ufr.friend_id = ufo.follower_id
      LEFT JOIN users u ON u.id = ufr.friend_id
      WHERE ufr.user_id = '${sessionId}'
      AND ufo.following_id = '${userId}'
      GROUP BY u.id
    `);

    const followersResult = await this.dataSource.query(`
      WITH FriendshipsOfSessionUser AS (
          -- Record of whether the session user is friend with the user listed
          SELECT
              COUNT(friend_id) AS is_friend,
              friend_id
          FROM user_friendships
          WHERE user_id = '${sessionId}'
          GROUP BY friend_id
      )
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university,
          fosu.is_friend AS is_friend
      FROM user_followers uf
      LEFT JOIN users u ON u.id = uf.follower_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code
      LEFT JOIN FriendshipsOfSessionUser fosu ON fosu.friend_id = u.id
      WHERE uf.following_id = '${userId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    const defaultReturn = {
      followers: followersResult.map((val) => {
        if (isFirstPerson) {
          return {
            userId: val.user_id,
            username: val.username,
            avatarUrl: val.avatar_url,
            university: val.university,
          };
        } else {
          return {
            userId: val.user_id,
            username: val.username,
            avatarUrl: val.avatar_url,
            university: val.university,
            isFriend: val.is_friend ? true : false,
          };
        }
      }),
      followerCount: followersResult.length,
      isFirstPerson,
    };

    if (q) {
      return {
        ...defaultReturn,
        friendUsernames: [],
        friendCount: 0,
      };
    } else {
      return {
        ...defaultReturn,
        friendUsernames: friendsResult.length ? friendsResult[0].friend_firstnames.split(',') : [],
        friendCount: friendsResult.length ? parseInt(friendsResult[0].friend_count) : 0,
      };
    }
  }

  async getFollowerFriendsByUserId(
    getFollowerFriendsByUserIdParams: GetFollowerFriendsByUserIdParams,
  ): Promise<GetFollowerFriendsByUserIdReturn> {
    const { sessionId, sessionRole, userId, q, limit, page } = getFollowerFriendsByUserIdParams;

    const isFirstPerson = sessionId === userId;
    const isCreator = sessionRole === 'creator';
    if (!isCreator) throw new HttpException('BAD_REQUEST', HttpStatus.BAD_REQUEST);

    const qClause = q ? `u.username ILIKE '${q}%' OR u.firstname ILIKE '${q}%'` : 'TRUE';

    const result = await this.dataSource.query(`
      SELECT
          u.id AS user_id,
          u.username AS username,
          u.avatar_url AS avatar_url,
          uni.university_name AS university
      FROM user_followers ufo
      INNER JOIN user_friendships ufr ON ufr.friend_id = ufo.follower_id
      LEFT JOIN users u ON u.id = ufr.friend_id
      LEFT JOIN universities uni ON uni.university_code = u.university_code_code
      WHERE ufr.user_id = '${sessionId}'
      AND ufo.following_id = '${userId}'
      AND ${qClause}
      LIMIT ${limit}
      OFFSET ${limit * (page - 1)};
    `);

    return {
      friends: result.map((val) => ({
        userId: val.user_id,
        username: val.username,
        avatarUrl: val.avatar_url,
        university: val.university,
      })),
      friendCount: result.length,
    };
  }

  async savePhoneContacts(savePhoneContactsParams: SavePhoneContactsParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, phoneNumbers } = savePhoneContactsParams;

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_contacts',
        values: phoneNumbers.map((val) => ({
          user_id: sessionId,
          phone_number: val,
        })),
      }),
    );

    return {};
  }

  async followByUserId(followByUserIdParams: FollowByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = followByUserIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't follow yourself", HttpStatus.BAD_REQUEST);
    }

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_followers',
        values: {
          follower_id: sessionId,
          following_id: userId,
        },
        onConflict: 'DO NOTHING',
      }),
    );

    return {};
  }

  async unfollowByUserId(unfollowByUserIdParams: UnfollowByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = unfollowByUserIdParams;

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_followers')
      .where({
        follower_id: sessionId,
        following_id: userId,
      })
      .execute();

    return {};
  }

  async removeFollowerByUserId(removeFollowerByUserIdParams: RemoveFollowerByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId, followerId } = removeFollowerByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_followers')
      .where({
        follower_id: followerId,
        following_id: userId,
      })
      .execute();

    return {};
  }

  async subscribeByUserId(subscribeByUserIdParams: SubscribeByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = subscribeByUserIdParams;

    if (sessionId === userId) {
      throw new HttpException("You can't subscribe to yourself", HttpStatus.BAD_REQUEST);
    }

    await this.dataSource.query(
      insertQueryBuilder({
        tableName: 'user_subscribers',
        values: {
          subscriber_id: sessionId,
          user_id: userId,
        },
        onConflict: 'DO NOTHING',
      }),
    );

    return {};
  }

  async unsubscribeByUserId(unsubscribeByUserIdParams: UnsubscribeByUserIdParams): Promise<EmptyReturn> {
    const { sessionId, sessionRole, userId } = unsubscribeByUserIdParams;

    await this.dataSource
      .createQueryBuilder()
      .delete()
      .from('user_subscribers')
      .where({
        subscriber_id: sessionId,
        user_id: userId,
      })
      .execute();

    return {};
  }

  async validateEmail(validateEmailParams: ValidateEmailParams): Promise<EmptyReturn> {
    const { ...rest } = validateEmailParams;
    return {};
  }

  async sendVerificationEmail(sendVerificationEmailParams: SendVerificationEmailParams): Promise<EmptyReturn> {
    const { ...rest } = sendVerificationEmailParams;
    return {};
  }

  async sendCreatorRequestForm(sendCreatorRequestFormParams: SendCreatorRequestFormParams): Promise<EmptyReturn> {
    const { ...rest } = sendCreatorRequestFormParams;
    return {};
  }

  async getUserBannerByUserId(
    getUserBannerByUserIdParams: GetUserBannerByUserIdParams,
  ): Promise<GetUserBannerByUserIdReturn> {
    const { sessionId, sessionRole, userId } = getUserBannerByUserIdParams;

    const isFirstPerson = sessionId === userId;
    if (!isFirstPerson) throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);

    const userBannerResult = await this.dataSource.query(`
      SELECT id AS user_id, username, avatar_url, firstname, lastname
      FROM users
      WHERE id = '${userId}';
    `);

    return {
      userId: userBannerResult[0].user_id,
      username: userBannerResult[0].username,
      avatarUrl: userBannerResult[0].avatar_url,
      fullname: userBannerResult[0].firstname + ' ' + userBannerResult[0].lastname,
    };
  }
}
