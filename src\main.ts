import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import * as cookieParser from 'cookie-parser';
import { AppModule } from './app.module';
import { AppLoggerInterceptor } from './interceptors/app-logger-interceptor';
import {
  ApiResponseObject,
  BasicAccountListItem,
  CommentItem,
  EmptyReturn,
  HobbyItem,
  IventCardItem,
  IventListItem,
  IventListItemWithIsFavorited,
  LocationItem,
  MemoryFolderCardItem,
  MemoryItem,
  SideMenuPageItem,
  UserListItem,
  UserListItemWithGroupRole,
  UserListItemWithPhoneNumber,
  UserListItemWithRelationshipStatus,
  VibeFolderCardItem,
  VibeItem,
} from './models';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Configure body parser with increased limits for large payloads (like base64 images)
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  app.useGlobalInterceptors(new AppLoggerInterceptor());

  app.enableCors({
    origin: '*', // Allow all origins, be cautious in production
    methods: 'GET,POST,PUT,DELETE',
    allowedHeaders: 'Content-Type, Authorization',
  });

  app.use(cookieParser());
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: true,
    }),
  );

  if (process.env.USE_SWAGGER !== 'false') {
    const config = new DocumentBuilder()
      .setTitle('IVENT APP EXAMPLE')
      .setDescription('DESCRIPTION')
      .setVersion('0.0.1')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
        'JWT-auth',
      )
      .addSecurityRequirements('JWT-auth')
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      extraModels: [
        ApiResponseObject,
        BasicAccountListItem,
        CommentItem,
        EmptyReturn,
        HobbyItem,
        IventCardItem,
        IventListItem,
        IventListItemWithIsFavorited,
        LocationItem,
        MemoryFolderCardItem,
        MemoryItem,
        SideMenuPageItem,
        UserListItem,
        UserListItemWithGroupRole,
        UserListItemWithPhoneNumber,
        UserListItemWithRelationshipStatus,
        VibeFolderCardItem,
        VibeItem,
      ],
    });

    const options = {
      explorer: true,
      swaggerOptions: {
        docExpansion: 'none',
        persistAuthorization: true,
      },
    };

    SwaggerModule.setup('api', app, document, options);
  }

  const port = process.env.SERVER_TCP_PORT ? Number(process.env.SERVER_TCP_PORT) : 3000;
  await app.listen(port, '0.0.0.0', () => {
    console.log(`Server is running at http://${process.env.SERVER_HOST}:${port}`);
    console.log(`Healthcheck at http://${process.env.SERVER_HOST}:${port}/health`);
    console.log(`Go to Swagger API -> http://${process.env.SERVER_HOST}:${port}/api`);
    console.log(`To download the api-json.json, go to -> http://${process.env.SERVER_HOST}:${port}/api-json`);
  });
}
bootstrap();
