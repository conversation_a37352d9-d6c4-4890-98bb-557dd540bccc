import { ApiProperty } from '@nestjs/swagger';
import { AuthEnum } from 'src/constants/enums/auth-enum';

export class ValidateReturn {
  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'JWT authentication token for authenticated users',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Unique identifier of the authenticated user',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'User role in the system',
    example: 'user',
  })
  role!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Username of the authenticated user',
    example: 'john_doe',
  })
  username!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Full name of the authenticated user',
    example: '<PERSON>',
  })
  fullname!: string | null;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: "URL to the user's avatar image",
    example: 'https://example.com/avatar.jpg',
    format: 'url',
  })
  avatarUrl!: string | null;

  @ApiProperty({
    type: 'string',
    enum: Object.values(AuthEnum),
    description: 'Authentication result type',
    example: AuthEnum.LOGIN,
  })
  type!: AuthEnum;
}
