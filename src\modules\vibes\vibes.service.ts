import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EmptyReturn } from 'src/models/empty-return';
import { DataSource } from 'typeorm';
import { FirebaseStorageService } from '../firebase/firebase-storage.service';
import {
  CreateVibeParams,
  DeleteVibeByVibeIdParams,
  GetCommentsByVibeIdParams,
  GetLikesByVibeIdParams,
  GetVibeByVibeIdParams,
  GetVibesParams,
  HideByVibeIdParams,
  LikeByVibeIdParams,
  ShowByVibeIdParams,
  UnlikeByVibeIdParams,
  UpdateByVibeIdParams,
} from './models/vibes.params';
import {
  CreateVibeReturn,
  GetCommentsByVibeIdReturn,
  GetLikesByVibeIdReturn,
  GetVibeByVibeIdReturn,
  GetVibesReturn,
} from './models/vibes.returns';

@Injectable()
export class VibesService {
  constructor(
    private dataSource: DataSource,
    private firebaseStorage: FirebaseStorageService,
  ) {}

  async createVibe(createVibeParams: CreateVibeParams): Promise<CreateVibeReturn> {
    const { ...rest } = createVibeParams;
    return {
      vibeId: '',
    };
  }

  async deleteByVibeId(deleteByVibeIdParams: DeleteVibeByVibeIdParams): Promise<EmptyReturn> {
    const { ...rest } = deleteByVibeIdParams;
    return {};
  }

  async getVibes(getVibesParams: GetVibesParams): Promise<GetVibesReturn> {
    const { sessionId, sessionRole, limit, page } = getVibesParams;

    const query = `
      WITH VibeRankings AS (
        SELECT
            v.id,
            v.vibe_folder_id,
            v.thumbnail_url,
            v.media_format,
            v.caption,
            v.created_at,
            v.creator_type,
            COALESCE(p.id, u.id) AS creator_id,
            COALESCE(p.page_name, u.username) AS creator_name,
            COALESCE(p.thumbnail_url, u.avatar_url) AS creator_image_url,
            COALESCE(i.id, s.ivent_id) AS ivent_id,
            COALESCE(i.ivent_name, ivs.ivent_name) AS ivent_name,
            (SELECT COUNT(*) FROM COMMENTS c WHERE c.vibe_id = v.id) AS comment_count,
            (SELECT COUNT(*) FROM vibe_likes vl WHERE vl.vibe_id = v.id) AS like_count,
            ROW_NUMBER() OVER (PARTITION BY v.vibe_folder_id ORDER BY v.created_at) AS vibe_index,
            COUNT(*) OVER (PARTITION BY v.vibe_folder_id) AS vibe_count,
            LEAD(v.id) OVER (PARTITION BY v.vibe_folder_id ORDER BY v.created_at) AS next_vibe_id,
            LAG(v.id) OVER (PARTITION BY v.vibe_folder_id ORDER BY v.created_at) AS previous_vibe_id
        FROM vibes v
        LEFT JOIN users u ON u.id = v.creator_user_id
        LEFT JOIN pages p ON p.id = v.creator_page_id
        LEFT JOIN ivents i ON i.vibe_folder_id = v.vibe_folder_id
        LEFT JOIN squads s ON s.vibe_folder_id = v.vibe_folder_id
        LEFT JOIN ivents ivs ON ivs.id = s.ivent_id
    )
    SELECT *
    FROM
        vibe_folders vf
        LEFT JOIN VibeRankings vr ON vr.vibe_folder_id = vf.id
    WHERE vf.thumbnail_url IS NOT NULL
    AND vr.vibe_index = 1
    LIMIT ${limit}
    OFFSET ${limit * (page - 1)};
    `;
    const queryResult = await this.dataSource.query(query);
    const mediaUrls = await Promise.all(
      queryResult.map(async (val) => {
        const extension = val.media_format == 'video' ? 'mp4' : 'jpg';
        const mediaUrl = await this.firebaseStorage.getSignedUrl(val.id, 'vibes', extension, Date.now() + 3600 * 1000);
        return mediaUrl;
      }),
    );

    return {
      vibes: queryResult.map((vibe, index) => ({
        content: {
          vibeId: vibe.id,
          vibeFolderId: vibe.vibe_folder_id,
          mediaUrl: mediaUrls[index],
          mediaFormat: vibe.media_format,
          thumbnailUrl: vibe.thumbnail_url,
          creatorId: vibe.creator_id,
          creatorType: vibe.creator_type,
          creatorUsername: vibe.creator_name,
          creatorAvatarUrl: vibe.creator_image_url,
          caption: vibe.caption,
          iventId: vibe.ivent_id,
          iventName: vibe.ivent_name,
          date: vibe.created_at,
          memberNames: [],
          memberCount: 0,
          createdAt: vibe.created_at,
          likeCount: parseInt(vibe.like_count),
          commentCount: parseInt(vibe.comment_count),
          nextVibeId: vibe.next_vibe_id,
          previousVibeId: vibe.previous_vibe_id,
          vibeIndex: parseInt(vibe.vibe_index) - 1,
          vibeCount: parseInt(vibe.vibe_count),
        },
        vibeFolderThumbnailUrl: vibe.thumbnail_url,
      })),
      vibeCount: 0,
    };
  }

  async getByVibeId(getByVibeIdParams: GetVibeByVibeIdParams): Promise<GetVibeByVibeIdReturn> {
    const { vibeId, ...rest } = getByVibeIdParams;

    const query = `
      WITH VibeRankings AS (
          SELECT
              id,
              ROW_NUMBER() OVER (PARTITION BY vibe_folder_id ORDER BY created_at) AS vibe_index,
              COUNT(*) OVER (PARTITION BY vibe_folder_id) AS vibe_count,
              LEAD(id) OVER (PARTITION BY vibe_folder_id ORDER BY created_at) AS next_vibe_id,
              LAG(id) OVER (PARTITION BY vibe_folder_id ORDER BY created_at) AS previous_vibe_id
          FROM vibes 
          WHERE vibe_folder_id = (SELECT vibe_folder_id FROM vibes WHERE id = $1)
      )
      SELECT
          v.id,
          v.vibe_folder_id,
          v.thumbnail_url,
          v.media_format,
          v.caption,
          v.created_at,
          v.creator_type,
          COALESCE(p.id, u.id) AS creator_id,
          COALESCE(p.page_name, u.username) AS creator_name,
          COALESCE(p.thumbnail_url, u.avatar_url) AS creator_image_url,
          vr.vibe_index AS vibe_index,
          vr.vibe_count AS vibe_count,
          vr.next_vibe_id AS next_vibe_id,
          vr.previous_vibe_id AS previous_vibe_id,
          COALESCE(i.id, s.ivent_id) AS ivent_id,
          COALESCE(i.ivent_name, ivs.ivent_name) AS ivent_name,
          (SELECT COUNT(*) FROM comments c WHERE c.vibe_id = v.id) AS comment_count,
          (SELECT COUNT(*) FROM vibe_likes vl WHERE vl.vibe_id = v.id) AS like_count
      FROM vibes v
      LEFT JOIN users u ON u.id = v.creator_user_id
      LEFT JOIN pages p ON p.id = v.creator_page_id
      LEFT JOIN ivents i ON i.vibe_folder_id = v.vibe_folder_id
      LEFT JOIN squads s ON s.vibe_folder_id = v.vibe_folder_id
      LEFT JOIN ivents ivs ON ivs.id = s.ivent_id
      LEFT JOIN VibeRankings vr ON vr.id = v.id
      WHERE v.id = $1;
    `;
    const [vibe] = await this.dataSource.query(query, [vibeId]);
    if (!vibe) throw new HttpException('NOT_FOUND', HttpStatus.NOT_FOUND);

    const extension = vibe.media_format == 'video' ? 'mp4' : 'jpg';
    const mediaUrl = await this.firebaseStorage.getSignedUrl(vibeId, 'vibes', extension, Date.now() + 3600 * 1000);

    return {
      vibeId,
      vibeFolderId: vibe.vibe_folder_id,
      mediaUrl,
      mediaFormat: vibe.media_format,
      thumbnailUrl: vibe.thumbnail_url,
      creatorId: vibe.creator_id,
      creatorType: vibe.creator_type,
      creatorUsername: vibe.creator_name,
      creatorAvatarUrl: vibe.creator_image_url,
      caption: vibe.caption,
      iventId: vibe.ivent_id,
      iventName: vibe.ivent_name,
      dates: vibe.created_at,
      memberFirstnames: [],
      memberCount: 0,
      createdAt: vibe.created_at,
      likeCount: parseInt(vibe.like_count),
      commentCount: parseInt(vibe.comment_count),
      nextVibeId: vibe.next_vibe_id,
      previousVibeId: vibe.previous_vibe_id,
      vibeIndex: parseInt(vibe.vibe_index) - 1,
      vibeCount: parseInt(vibe.vibe_count),
    };
  }

  async getCommentsByVibeId(getCommentsByVibeIdParams: GetCommentsByVibeIdParams): Promise<GetCommentsByVibeIdReturn> {
    const { ...rest } = getCommentsByVibeIdParams;
    return {
      comments: [],
      commentCount: 0,
    };
  }

  async updateByVibeId(updateByVibeIdParams: UpdateByVibeIdParams): Promise<EmptyReturn> {
    const { ...rest } = updateByVibeIdParams;
    return {};
  }

  async getLikesByVibeId(getLikesByVibeIdParams: GetLikesByVibeIdParams): Promise<GetLikesByVibeIdReturn> {
    const { ...rest } = getLikesByVibeIdParams;
    return {
      likes: [],
      likeCount: 0,
    };
  }

  async likeByVibeId(likeByVibeIdParams: LikeByVibeIdParams): Promise<EmptyReturn> {
    const { ...rest } = likeByVibeIdParams;
    return {};
  }

  async unlikeByVibeId(unlikeByVibeIdParams: UnlikeByVibeIdParams): Promise<EmptyReturn> {
    const { ...rest } = unlikeByVibeIdParams;
    return {};
  }

  async hideByVibeId(hideByVibeIdParams: HideByVibeIdParams): Promise<EmptyReturn> {
    const { ...rest } = hideByVibeIdParams;
    return {};
  }

  async showByVibeId(showByVibeIdParams: ShowByVibeIdParams): Promise<EmptyReturn> {
    const { ...rest } = showByVibeIdParams;
    return {};
  }
}
