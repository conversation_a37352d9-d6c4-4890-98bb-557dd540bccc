import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUrl, IsUUID, Length, Matches } from 'class-validator';

export class CreateCommentDto {
  @ApiProperty({
    type: 'string',
    description: 'The comment text content',
    example: 'This is a great post!',
    required: true,
    minLength: 1,
    maxLength: 1000,
  })
  @IsString()
  @Length(1, 1000, { message: 'Comment must be between 1 and 1000 characters' })
  comment!: string;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the vibe being commented on',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'vibeId must be a valid UUID v4' })
  vibeId!: string;

  @ApiProperty({
    type: 'string',
    description: 'An ivent name can only contain letters, numbers, underscores, and hyphens.',
    example: 'my_awesome_ivent',
    required: true,
    minLength: 3,
    maxLength: 200,
  })
  @IsString()
  @Matches(/^[a-zA-Z0-9_\-]{3,200}$/, {
    message: 'Ivent name must contain only letters, numbers, underscores, and hyphens (3-200 characters)',
  })
  iventName!: string;

  @ApiProperty({
    type: 'string',
    nullable: true,
    description: 'Optional thumbnail URL for the comment',
    example: 'https://example.com/thumbnail.jpg',
    required: false,
    format: 'url',
  })
  @IsUrl({}, { message: 'Thumbnail URL must be a valid URL' })
  @IsOptional()
  thumbnailUrl!: string | null;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the user creating the comment',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'creatorId must be a valid UUID v4' })
  creatorId!: string;
}
