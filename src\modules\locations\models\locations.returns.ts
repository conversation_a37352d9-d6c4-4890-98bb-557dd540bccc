import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { LocationItem } from 'src/models';

export class GetLatestLocationsReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(LocationItem) },
    description: 'List of latest locations',
    example: [],
  })
  locations!: LocationItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of latest locations',
    example: 0,
    minimum: 0,
  })
  locationCount!: number;
}

export class GetLocationsReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(LocationItem) },
    description: 'List of locations',
    example: [],
  })
  locations!: LocationItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of locations',
    example: 0,
    minimum: 0,
  })
  locationCount!: number;
}

export class GetPlacesReturn {
  @ApiProperty({
    type: 'array',
    items: { $ref: getSchemaPath(LocationItem) },
    description: 'List of places',
    example: [],
  })
  places!: LocationItem[];

  @ApiProperty({
    type: 'number',
    description: 'Total number of places',
    example: 0,
    minimum: 0,
  })
  placeCount!: number;
}
